/**
 * Unified Notification System for Power Pages
 */
const NotificationSystem = {
  config: {
    defaultTimeout: 5000,
    fadeInDuration: 300,
    fadeOutDuration: 300,
    autoHide: true,
    showIcons: true
  },

  icons: {
    success: 'fas fa-check-circle',
    error: 'fas fa-exclamation-circle',
    warning: 'fas fa-exclamation-triangle',
    info: 'fas fa-info-circle',
    loading: 'fas fa-spinner fa-spin'
  },

  show: function(type, message, options = {}) {
    const settings = { ...this.config, ...options };
    let container = this.getNotificationContainer(settings.containerId);
    if (!container) {
      console.warn('Notification container not found');
      return;
    }

    if (settings.clearExisting !== false) {
      this.clear(container);
    }

    const notification = this.createNotification(type, message, settings);
    container.appendChild(notification);
    notification.classList.add('notification-fade-in');

    if (settings.autoHide && settings.timeout > 0) {
      setTimeout(() => {
        this.hide(notification);
      }, settings.timeout || this.config.defaultTimeout);
    }

    this.setAriaLiveRegion(notification, type);
    return notification;
  },

  showSuccess: function(message, options = {}) {
    return this.show('success', message, {
      ...options,
      timeout: options.timeout || 4000,
      title: options.title || 'Success'
    });
  },

  showError: function(message, options = {}) {
    return this.show('error', message, {
      ...options,
      timeout: options.timeout || 0,
      title: options.title || 'Error'
    });
  },

  showWarning: function(message, options = {}) {
    return this.show('warning', message, {
      ...options,
      timeout: options.timeout || 6000,
      title: options.title || 'Warning'
    });
  },

  showInfo: function(message, options = {}) {
    return this.show('info', message, {
      ...options,
      timeout: options.timeout || 5000,
      title: options.title || 'Information'
    });
  },

  showLoading: function(message, options = {}) {
    return this.show('loading', message, {
      ...options,
      timeout: 0,
      title: options.title || 'Loading',
      autoHide: false
    });
  },

  createNotification: function(type, message, settings) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.setAttribute('role', type === 'error' ? 'alert' : 'status');
    notification.setAttribute('aria-live', type === 'error' ? 'assertive' : 'polite');

    let content = '';
    if (settings.showIcons && this.icons[type]) {
      content += `<div class="notification-icon"><i class="${this.icons[type]}" aria-hidden="true"></i></div>`;
    }

    content += '<div class="notification-content">';
    if (settings.title) {
      content += `<div class="notification-title">${this.escapeHtml(settings.title)}</div>`;
    }
    content += `<div class="notification-message">${this.escapeHtml(message)}</div>`;
    content += '</div>';

    notification.innerHTML = content;
    if (settings.compact) {
      notification.classList.add('notification-compact');
    }

    return notification;
  },

  getNotificationContainer: function(containerId) {
    if (containerId) {
      return document.getElementById(containerId);
    }

    const commonIds = ['notificationContainer', 'messageContainer', 'alertContainer'];
    for (const id of commonIds) {
      const container = document.getElementById(id);
      if (container) return container;
    }

    const jquerySelectors = ['#errorMessage', '#successMessage', '#messageContent'];
    for (const selector of jquerySelectors) {
      const element = $(selector);
      if (element.length > 0) {
        return element[0];
      }
    }

    return this.createDefaultContainer();
  },

  createDefaultContainer: function() {
    const container = document.createElement('div');
    container.id = 'notificationContainer';
    container.className = 'notification-container';

    const mainContent = document.querySelector('.card-body, .container, main, body');
    if (mainContent) {
      mainContent.insertBefore(container, mainContent.firstChild);
      return container;
    }

    return null;
  },

  hide: function(notification) {
    if (!notification || !notification.parentNode) return;
    notification.style.transition = `opacity ${this.config.fadeOutDuration}ms ease`;
    notification.style.opacity = '0';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, this.config.fadeOutDuration);
  },

  clear: function(container) {
    if (!container) {
      container = this.getNotificationContainer();
    }

    if (container) {
      const notifications = container.querySelectorAll('.notification, .message, .alert');
      notifications.forEach(notification => {
        this.hide(notification);
      });
    }

    $('#errorMessage, #successMessage').hide();
  },

  setAriaLiveRegion: function(notification, type) {
    if (type === 'error') {
      notification.setAttribute('aria-live', 'assertive');
      notification.setAttribute('role', 'alert');
    } else {
      notification.setAttribute('aria-live', 'polite');
      notification.setAttribute('role', 'status');
    }
  },

  escapeHtml: function(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
};

const SecureConfig = {
  getFunctionUrl(functionName = 'PasswordService') {
    const baseUrl = window.appConfig?.functionUrl;
    if (!baseUrl) {
      return null;
    }
    return `${baseUrl}/api/${functionName}`;
  },

  getFunctionKey() {
    const functionKey = window.appConfig?.passwordFunctionKey;
    if (!functionKey || functionKey.includes('ERROR_MISSING')) {
      return null;
    }
    return functionKey;
  },

  buildSecureUrl(functionName, operation) {
    const baseUrl = this.getFunctionUrl(functionName);
    const functionKey = this.getFunctionKey();

    if (!baseUrl || !functionKey) {
      return null;
    }

    return `${baseUrl}?operation=${operation}&code=${functionKey}`;
  }
};

// Add remaining NotificationSystem methods
NotificationSystem.handleError = function(error, context = {}) {
  console.error('Error in context:', context, error);

  if (error.status) {
    return this.handleHttpError(error, context);
  }

  if (error instanceof TypeError && error.message.includes('fetch')) {
    return this.showError('Network connection failed. Please check your internet connection and try again.', {
      title: 'Connection Error',
      timeout: 0
    });
  }

  if (error.name === 'AbortError' || error.message.includes('timeout')) {
    return this.showError('Request timed out. Please check your connection and try again. If the problem persists, try refreshing the page.', {
      title: 'Request Timeout',
      timeout: 0
    });
  }

  if (error.message.includes('configuration') || error.message.includes('missing')) {
    return this.showError('System configuration error. Please contact support if this persists.', {
      title: 'Configuration Error',
      timeout: 0
    });
  }

  if (error.message.includes('rate limit') || error.message.includes('429')) {
    return this.showWarning(error.message, {
      title: 'Rate Limit',
      timeout: 10000
    });
  }

  if (error.message.includes('401') || error.message.includes('unauthorized')) {
    return this.showError('Authentication failed. Please refresh the page and try again.', {
      title: 'Authentication Error',
      timeout: 0
    });
  }

  const userMessage = error.message || 'An unexpected error occurred. Please try again.';
  return this.showError(userMessage, {
    title: 'Error',
    timeout: 0
  });
};

NotificationSystem.handleHttpError = function(error, context = {}) {
  const status = error.status;

  if (status >= 400 && status < 500) {
    switch (status) {
      case 400:
        return this.showError('Invalid request. Please check your input and try again.', {
          title: 'Invalid Request',
          timeout: 0
        });
      case 401:
        return this.showError('Authentication required. Please refresh the page and try again.', {
          title: 'Authentication Required',
          timeout: 0
        });
      case 403:
        return this.showError('Access denied. You do not have permission to perform this action.', {
          title: 'Access Denied',
          timeout: 0
        });
      case 404:
        return this.showError('Service not found. Please contact support if this persists.', {
          title: 'Service Unavailable',
          timeout: 0
        });
      case 409:
        const conflictMessage = error.message || 'A conflict occurred. Please review your input.';
        if (conflictMessage.toLowerCase().includes('password') &&
            (conflictMessage.toLowerCase().includes('reuse') ||
             conflictMessage.toLowerCase().includes('history') ||
             conflictMessage.toLowerCase().includes('recent'))) {
          return this.showError(conflictMessage, {
            title: 'Password Reuse Detected',
            timeout: 0
          });
        }
        return this.showError(conflictMessage, {
          title: 'Conflict',
          timeout: 0
        });
      case 429:
        return this.showWarning('Too many requests. Please wait a moment before trying again.', {
          title: 'Rate Limit Exceeded',
          timeout: 15000
        });
      default:
        return this.showError('Request failed. Please try again or contact support if the problem persists.', {
          title: 'Request Failed',
          timeout: 0
        });
    }
  }

  if (status >= 500) {
    return this.showError('A server error occurred. Please try again later or contact support.', {
      title: 'Server Error',
      timeout: 0
    });
  }

  return this.showError('An unexpected error occurred. Please try again.', {
    title: 'Unexpected Error',
    timeout: 0
  });
};

NotificationSystem.validateConfiguration = function(requiredConfig = []) {
  const missing = [];
  for (const configKey of requiredConfig) {
    if (!window.appConfig || !window.appConfig[configKey]) {
      missing.push(configKey);
    }
  }

  if (missing.length > 0) {
    this.showError(`Missing configuration: ${missing.join(', ')}. Please contact support.`, {
      title: 'Configuration Error',
      timeout: 0
    });
    return false;
  }

  return true;
};

NotificationSystem.validateDOMElements = function(elementSelectors = []) {
  const missing = [];
  for (const selector of elementSelectors) {
    const element = document.querySelector(selector);
    if (!element) {
      missing.push(selector);
    }
  }

  if (missing.length > 0) {
    console.error('Missing DOM elements:', missing);
    this.showError('Page elements missing. Please refresh the page.', {
      title: 'Page Error',
      timeout: 0
    });
    return false;
  }

  return true;
};

NotificationSystem.checkBrowserCompatibility = function() {
  const requiredFeatures = [];
  
  // Check for fetch API
  if (typeof fetch === 'undefined') {
    requiredFeatures.push('Fetch API');
  }
  
  // Check for Promise support
  if (typeof Promise === 'undefined') {
    requiredFeatures.push('Promise support');
  }
  
  // Check for modern JavaScript features
  if (typeof URLSearchParams === 'undefined') {
    requiredFeatures.push('URL Parameters');
  }
  
  // Check for localStorage/sessionStorage
  try {
    if (typeof localStorage === 'undefined' || typeof sessionStorage === 'undefined') {
      requiredFeatures.push('Local Storage');
    }
  } catch (e) {
    requiredFeatures.push('Local Storage');
  }
  
  // Check for modern DOM methods
  if (!document.querySelector || !document.querySelectorAll) {
    requiredFeatures.push('Modern DOM methods');
  }
  
  if (requiredFeatures.length > 0) {
    // Use basic DOM manipulation for compatibility message
    const message = `Your browser is missing required features: ${requiredFeatures.join(', ')}. Please update to a modern browser (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+) to use this application.`;
    
    // Try to show error using NotificationSystem, fallback to alert
    try {
      this.showError(message, {
        title: 'Browser Compatibility Issue',
        timeout: 0
      });
    } catch (e) {
      // Fallback to basic alert if NotificationSystem fails
      alert('Browser Compatibility Issue: ' + message);
    }
    
    return false;
  }
  
  return true;
};

const PASSWORD_SERVICE_URL = SecureConfig.getFunctionUrl('PasswordService');
const APPLICATION_NAME = window.appConfig?.applicationName || "";

// Simple flag for password reuse detection
const PASSWORD_REUSE_FLAG = 'PASSWORD_REUSE';

const urlParams = new URLSearchParams(window.location.search);
const RESET_TOKEN = urlParams.get('token');



if (!PASSWORD_SERVICE_URL) {
  NotificationSystem.showError("PasswordService URL not configured");
}

if (!RESET_TOKEN) {
  console.error('❌ No reset token found in URL. Expected format: ?token=<reset-token>');
  $(document).ready(function() {
    // Check browser compatibility first
    if (!NotificationSystem.checkBrowserCompatibility()) {
      return; // Stop initialization if browser is incompatible
    }
    showUnauthorizedAccess('No reset token provided. Please request a new password reset.');
  });
} else {
  console.log('✅ Reset token found, validating...');
  $(document).ready(function() {
    // Check browser compatibility first
    if (!NotificationSystem.checkBrowserCompatibility()) {
      return; // Stop initialization if browser is incompatible
    }
    validateResetTokenAccess(RESET_TOKEN);
  });
}

const InputSanitizer = {
  sanitizeInput(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
  },

  validatePassword(password) {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar;
  }
};

const errorMessageDiv = $('#errorMessage');
const successMessageDiv = $('#successMessage');
const submitButton = $('#submitButton');
const passwordForm = $('#passwordForm');
const verificationCodeInput = $('#verificationCode');
const newPasswordInput = $('#newPassword');
const confirmPasswordInput = $('#confirmPassword');
const toggleButtons = {
  newPassword: $('#toggleNewPassword'),
  confirmPassword: $('#toggleConfirmPassword')
};
// Unified notification functions
function showMessage(message, isError = true, timeout = 0) {
  const type = isError ? 'error' : 'success';
  const options = timeout > 0 ? { timeout: timeout } : {};
  return NotificationSystem.show(type, message, options);
}

function showPasswordReuseError(message) {
  // Display prominent error message with enhanced styling
  NotificationSystem.showError(`Password Cannot Be Reused: ${message}`, {
    title: 'Password Reuse Detected',
    timeout: 0 // Don't auto-hide password reuse errors
  });

  // Clear password fields and focus for retry
  $('#newPassword').val('').focus();
  $('#confirmPassword').val('');
  $('#newPassword, #confirmPassword').removeClass('is-invalid');
  $('.invalid-feedback').text('');
}

function clearMessages() {
  NotificationSystem.clear();
  // Also clear legacy elements for compatibility
  errorMessageDiv.hide();
  successMessageDiv.hide();
}

function showSuccess(message, timeout = 4000) {
  return NotificationSystem.showSuccess(message, { timeout: timeout });
}

function showError(message) {
  return NotificationSystem.showError(message);
}

function showLoading(message) {
  return NotificationSystem.showLoading(message);
}

function showSuccessNotification(title, message) {
  const modalHtml = `
    <div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header bg-success text-white">
            <h5 class="modal-title" id="successModalLabel">
              <i class="fas fa-check-circle me-2"></i>${title}
            </h5>
          </div>
          <div class="modal-body text-center">
            <div class="mb-3">
              <i class="fas fa-check-circle text-success" style="font-size: 3rem;"></i>
            </div>
            <p class="mb-0">${message}</p>
            <p class="text-muted mt-2">You will be redirected shortly...</p>
          </div>
        </div>
      </div>
    </div>
  `;

  $('#successModal').remove();

  $('body').append(modalHtml);

  const modal = new bootstrap.Modal(document.getElementById('successModal'), {
    backdrop: 'static',
    keyboard: false
  });
  modal.show();

  setTimeout(() => {
    modal.hide();
  }, 2500);
}
function togglePasswordVisibility(inputId, toggleButton) {
  const input = $(`#${inputId}`);
  const icon = toggleButton.find('i');
  
  if (input.attr('type') === 'password') {
    input.attr('type', 'text');
    icon.removeClass('fa-eye').addClass('fa-eye-slash');
  } else {
    input.attr('type', 'password');
    icon.removeClass('fa-eye-slash').addClass('fa-eye');
  }
}


function validateNewPassword() {
  const password = newPasswordInput.val();
  const passwordError = $('#newPasswordError');

  // Clear previous validation state
  newPasswordInput.removeClass('is-invalid is-valid');
  newPasswordInput.closest('.form-group').removeClass('has-error');

  if (!password) {
    passwordError.text('Password is required');
    newPasswordInput.addClass('is-invalid');
    newPasswordInput.closest('.form-group').addClass('has-error');
    newPasswordInput.focus();
    return false;
  }

  if (!InputSanitizer.validatePassword(password)) {
    passwordError.text('Password must be at least 8 characters with uppercase, lowercase, number, and special character');
    newPasswordInput.addClass('is-invalid');
    newPasswordInput.closest('.form-group').addClass('has-error');
    newPasswordInput.focus();
    return false;
  }

  passwordError.text('');
  newPasswordInput.removeClass('is-invalid').addClass('is-valid');
  return true;
}

function validateConfirmPassword() {
  const password = newPasswordInput.val();
  const confirmPassword = confirmPasswordInput.val();
  const confirmPasswordError = $('#confirmPasswordError');
  
  if (!confirmPassword) {
    confirmPasswordError.text('Please confirm your password');
    confirmPasswordInput.addClass('is-invalid');
    return false;
  }
  
  if (password !== confirmPassword) {
    confirmPasswordError.text('Passwords do not match');
    confirmPasswordInput.addClass('is-invalid');
    return false;
  }
  
  confirmPasswordError.text('');
  confirmPasswordInput.removeClass('is-invalid');
  return true;
}

function validateVerificationCode() {
  const verificationCode = verificationCodeInput.val();
  const verificationCodeError = $('#verificationCodeError');

  if (!verificationCode) {
    verificationCodeError.text('Verification code is required');
    verificationCodeInput.addClass('is-invalid');
    return false;
  }

  if (!/^\d{6}$/.test(verificationCode)) {
    verificationCodeError.text('Verification code must be exactly 6 digits');
    verificationCodeInput.addClass('is-invalid');
    return false;
  }

  verificationCodeError.text('');
  verificationCodeInput.removeClass('is-invalid');
  return true;
}

function validateForm() {
  const isVerificationCodeValid = validateVerificationCode();
  const isNewPasswordValid = validateNewPassword();
  const isConfirmPasswordValid = validateConfirmPassword();

  return isVerificationCodeValid && isNewPasswordValid && isConfirmPasswordValid;
}

async function performPasswordReset(newPassword, verificationCode) {
  try {
    const sanitizedPassword = InputSanitizer.sanitizeInput(newPassword);
    const sanitizedVerificationCode = InputSanitizer.sanitizeInput(verificationCode);

    if (!InputSanitizer.validatePassword(sanitizedPassword)) {
      throw new Error('Invalid password format');
    }

    if (!RESET_TOKEN) {
      throw new Error('Reset token is missing');
    }

    if (!sanitizedVerificationCode) {
      throw new Error('Verification code is required');
    }

    const requestBody = {
      token: RESET_TOKEN,
      verificationCode: sanitizedVerificationCode,
      newPassword: sanitizedPassword,
      applicationName: APPLICATION_NAME
    };

    const apiUrl = SecureConfig.buildSecureUrl('PasswordService', 'reset-complete');
    if (!apiUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }



    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '3.0.0-simplified',
        'X-Requested-With': 'XMLHttpRequest' // CSRF protection
      },
      body: JSON.stringify(requestBody)
    });

    console.log('API Response Status:', response.status, response.statusText);

    if (response.status === 429) {
      const responseText = await response.text();
      let result;
      try {
        result = JSON.parse(responseText);
        if (result.retryAfter) {
          const retryAfter = new Date(result.retryAfter);
          const waitTime = Math.ceil((retryAfter - new Date()) / 1000);
          throw new Error(`Too many requests. Please wait ${waitTime} seconds before trying again.`);
        }
      } catch (parseError) {
        throw new Error('Too many requests. Please wait a moment before trying again.');
      }
      throw new Error('Too many requests. Please wait a moment before trying again.');
    }

    const responseText = await response.text();
    console.log('API Response Text:', responseText);

    let result;
    if (responseText) {
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('JSON Parse Error:', parseError);
        throw new Error('Invalid response from server. Please try again.');
      }
    } else {
      throw new Error('Empty response from server. Please try again.');
    }

    if (response.ok) {
      const responseData = result.data || result;
      return {
        success: true,
        message: responseData.message || result.message || "Password updated successfully!",
        email: responseData.email || result.email,
        requiresLogout: responseData.requiresLogout || result.requiresLogout
      };
    } else if (response.status === 409 && result.action === "ShowBlockPage") {
      // Password reuse error - throw with special flag
      const error = new Error(result.userMessage || "Password has been used recently. Please choose a different password.");
      error.type = PASSWORD_REUSE_FLAG;
      throw error;
    } else {
      const errorMessage = result.message || (result.data && result.data.message) || "Password operation failed. Please try again.";
      throw new Error(errorMessage);
    }

  } catch (error) {
    console.error("Password operation error:", error);
    throw error;
  }
}

function initializeFormHandlers() {
  verificationCodeInput.blur(function() {
    validateVerificationCode();
  });

  verificationCodeInput.on('input', function() {
    $(this).removeClass('is-invalid');
    $('#verificationCodeError').text('');
  });

  newPasswordInput.blur(function() {
    validateNewPassword();
  });

  confirmPasswordInput.blur(function() {
    validateConfirmPassword();
  });

  newPasswordInput.on('input', function() {
    $(this).removeClass('is-invalid');
    $('#newPasswordError').text('');
  });

  confirmPasswordInput.on('input', function() {
    $(this).removeClass('is-invalid');
    $('#confirmPasswordError').text('');
  });

  toggleButtons.newPassword.click(function() {
    togglePasswordVisibility('newPassword', $(this));
  });

  toggleButtons.confirmPassword.click(function() {
    togglePasswordVisibility('confirmPassword', $(this));
  });

  passwordForm.submit(async function(event) {
    event.preventDefault();
    clearMessages();

    try {
      if (!validateForm()) {
        return;
      }

      submitButton.prop("disabled", true);
      submitButton.text("Resetting...");

      const newPassword = newPasswordInput.val();
      const verificationCode = verificationCodeInput.val();

      showMessage("Resetting your password...", false);

      const result = await performPasswordReset(newPassword, verificationCode);

      if (result.success) {
        showMessage("Password reset successfully!", false);
        passwordForm[0].reset();

        $('input').removeClass('is-invalid');
        $('.invalid-feedback').text('');

        showSuccessNotification("Password Reset Successful!", "Your password has been reset successfully. You can now log in with your new password.");

        setTimeout(() => {
          window.location.href = '/?message=' + encodeURIComponent('Password reset successfully. You can now log in with your new password.');
        }, 3000);
      }

    } catch (error) {
      console.error("Form submission error:", error);

      if (error.type === PASSWORD_REUSE_FLAG) {
        showPasswordReuseError(error.message);
      } else {
        // Use enhanced error handling
        NotificationSystem.handleError(error, {
          operation: 'reset-password',
          verificationCode: verificationCodeInput.val()
        });
      }
    } finally {
      submitButton.prop("disabled", false);
      submitButton.text("Reset Password");
    }
  });
}

function initializeResetForm() {
  submitButton.text('Reset Password');
}

async function validateResetTokenAccess(token) {
  try {
    const secureUrl = SecureConfig.buildSecureUrl('PasswordService', 'validate-reset-token');
    if (!secureUrl) {
      throw new Error('Azure Function configuration missing. Please check Power Pages settings.');
    }

    const response = await fetch(secureUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: token
      })
    });

    const result = await response.json();
    const data = result.data || result;

    if (!data.success) {
      showUnauthorizedAccess(data.message || 'Invalid or expired reset token.');
      return;
    }

    showTokenValidationSuccess();

  } catch (error) {
    NotificationSystem.handleError(error, { context: 'token validation' });
    showUnauthorizedAccess('Unable to validate reset token. Please try again.');
  }
}

function showUnauthorizedAccess(message) {

  sessionStorage.setItem('resetError', JSON.stringify({
    message: message,
    timestamp: new Date().toISOString(),
    source: 'reset-token-validation'
  }));

  const errorPageUrl = '/Reset-Error/';

  try {
    window.location.href = errorPageUrl;
  } catch (error) {
    // Fallback to forgot password page if error page not found
    window.location.href = '/Forgot-Password/';
  }
}

function showTokenValidationSuccess() {
  const pageHeader = document.querySelector('.card-header h3');
  if (pageHeader) {
    pageHeader.innerHTML = '<i class="fas fa-check-circle text-success"></i> Reset Password <small class="text-success">(Token Verified)</small>';
  }

  // Initialize form handlers only after successful token validation
  initializeResetForm();
  initializeFormHandlers();
}

$(document).ready(function() {
  // Form handlers will be initialized only after successful token validation
  // This prevents form interaction with invalid tokens
});
