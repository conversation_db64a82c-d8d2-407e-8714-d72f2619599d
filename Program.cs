using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Azure.Storage.Blobs;
using Azure.Identity;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Graph;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;
using System.Text.Json;
using Microsoft.Azure.Functions.Worker;


var host = new HostBuilder()
    .ConfigureFunctionsWorkerDefaults()
    .ConfigureAppConfiguration((context, builder) =>
    {
        builder.AddEnvironmentVariables()
               .AddUserSecrets<Program>();

        var settings = builder.Build();

        var keyVaultUrl = settings.GetValue<string>("KeyVaultUrl");
        if (!string.IsNullOrEmpty(keyVaultUrl))
        {
            // Validate Key Vault URL is not a placeholder
            if (keyVaultUrl == "https://your-keyvault-name.vault.azure.net/")
            {
                // Log warning instead of throwing - allow app to start with degraded functionality
                Console.WriteLine("WARNING: KeyVaultUrl contains placeholder value. Key Vault integration disabled. Set your actual Azure Key Vault URL for production use.");
            }
            else
            {
                try
                {
                    builder.AddAzureKeyVault(
                        new Uri(keyVaultUrl),
                        new DefaultAzureCredential(
                            new DefaultAzureCredentialOptions
                            {
                                ManagedIdentityClientId = settings.GetValue<string>("UserAssignedClientId")
                            }));
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"WARNING: Failed to configure Key Vault: {ex.Message}. Continuing without Key Vault integration.");
                }
            }
        }
    })
    .ConfigureServices((context, services) =>
    {
        var configuration = context.Configuration;

        services.AddApplicationInsightsTelemetryWorkerService();
        services.ConfigureFunctionsApplicationInsights();

        services.AddMemoryCache();
        services.AddSingleton<IConfiguration>(configuration);

        // Create logger for configuration validation
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var logger = loggerFactory.CreateLogger("ConfigurationValidation");

        // Log configuration issues instead of throwing exceptions - allows app to start with degraded functionality
        var configurationValid = ConfigurationValidator.LogConfigurationIssues(configuration, logger);
        if (!configurationValid)
        {
            logger.LogWarning("Application starting with degraded functionality due to missing configuration. Some features may not work correctly.");
        }

        // Validate optional configuration and log warnings for values outside recommended ranges
        ConfigurationValidator.ValidateOptionalConfiguration(configuration, logger);

        services.Configure<SendGridOptions>(configuration.GetSection(SendGridOptions.SectionName));
        services.Configure<EntraOptions>(configuration.GetSection(EntraOptions.SectionName));
        services.Configure<PasswordResetOptions>(configuration.GetSection(PasswordResetOptions.SectionName));
        services.Configure<AccountRegistrationOptions>(configuration.GetSection(AccountRegistrationOptions.SectionName));
        services.Configure<StorageOptions>(options =>
        {
            options.ConnectionString = configuration["AzureWebJobsStorage"] ?? configuration["Storage:ConnectionString"] ?? string.Empty;
        });
        services.Configure<RateLimitOptions>(configuration.GetSection(RateLimitOptions.SectionName));
        services.Configure<InvitationOptions>(configuration.GetSection(InvitationOptions.SectionName));

        // Blob Storage client for password history - fail-fast validation
        services.AddSingleton<BlobServiceClient>(serviceProvider =>
        {
            var storageOptions = serviceProvider.GetRequiredService<IOptions<StorageOptions>>();
            var connectionString = storageOptions.Value.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("Storage connection string is required. Configure AzureWebJobsStorage or Storage:ConnectionString in application settings or Azure Key Vault.");
            }

            return new BlobServiceClient(connectionString);
        });

        services.AddHttpClient("default")
                .AddStandardResilienceHandler(options =>
                {
                    options.CircuitBreaker.SamplingDuration = TimeSpan.FromSeconds(30);
                    options.AttemptTimeout.Timeout = TimeSpan.FromSeconds(15);
                });

        services.AddHttpClient();

        services.AddScoped<IPasswordHistoryService, PasswordHistoryService>();
        services.AddScoped<IEmailService, EmailService>();

        // Graph API client for Entra External ID - fail-fast validation
        services.AddScoped<GraphServiceClient>(serviceProvider =>
        {
            var entraOptions = serviceProvider.GetRequiredService<IOptions<EntraOptions>>();
            var options = entraOptions.Value;

            if (string.IsNullOrEmpty(options.ClientId) || string.IsNullOrEmpty(options.ClientSecret) || string.IsNullOrEmpty(options.TenantId))
            {
                throw new InvalidOperationException("Entra External ID configuration is incomplete. Configure EntraExternalID:ClientId, ClientSecret, and TenantId in application settings or Azure Key Vault.");
            }

            var credential = new ClientSecretCredential(options.TenantId, options.ClientId, options.ClientSecret);
            return new GraphServiceClient(credential);
        });

        // Utility and token management
        services.AddSingleton<RateLimitService>();
        services.AddSingleton<ResetTokenManager>();
        services.AddSingleton<InvitationTokenManager>();

        // Functions
        services.AddScoped<PasswordHistoryValidator.PasswordFunction>();
        services.AddScoped<PasswordHistoryValidator.UtilityFunction>();
        services.AddScoped<PasswordHistoryValidator.InvitationFunction>();
        services.AddScoped<PasswordHistoryValidator.RegistrationFunction>();

        services.AddSingleton<JsonSerializerOptions>(provider => new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true,
            WriteIndented = false
        });
    })
    .Build();

host.Run();
