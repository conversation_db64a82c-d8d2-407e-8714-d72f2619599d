<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="power-pages-styles.css">
<link rel="stylesheet" href="forgot-password.css">


    <meta name="azure-function-url" content="{{ settings['AzureFunctionUrl'] | default: '' }}">
    <meta name="password-function-key" content="{{ settings['Password Function Key'] | default: '' }}">
    <meta name="application-name" content="{{ settings['ApplicationName'] | default: '' }}">

    <script>
      function getConfig() {
        const functionUrl = document.querySelector('meta[name="azure-function-url"]')?.content;
        const passwordFunctionKey = document.querySelector('meta[name="password-function-key"]')?.content;
        const applicationName = document.querySelector('meta[name="application-name"]')?.content;

        return {
          functionUrl: functionUrl || null,
          passwordFunctionKey: passwordFunctionKey || null,
          applicationName: applicationName || null
        };
      }

      window.appConfig = getConfig();

  // Secure user data extraction with input sanitization
  function sanitizeUserInput(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
  }

  // Store in secure global object
  window.liquidUser = {
    applicationName: window.appConfig.applicationName
  };


</script>

<div class="col-lg-12 columnBlockLayout">
<div class="row sectionBlockLayout text-start">
  <div class="container container-flex">
    <div class="container forgot-password-container">
      <h2>Reset / Forgot Password</h2>
      <p>Enter your email address and we'll send you a link to reset your password.</p>

      <!-- Unified Notification Container -->
      <div id="notificationContainer" class="notification-container"></div>

      <!-- Legacy notification elements for compatibility -->
      <div id="errorMessage" class="alert alert-danger d-none"></div>
      <div id="successMessage" class="alert alert-success d-none"></div>
      
      <form id="forgotPasswordForm">

        <div class="form-group mb-3">
          <label for="email" class="form-label fw-bold">Email Address</label>
          <input type="email" id="email" required class="form-control" placeholder="Enter your email address" autocomplete="username">
          <div class="form-text">We'll send password reset instructions to this email address.</div>
          <div class="invalid-feedback"></div>
        </div>

        <button type="submit" id="resetButton" class="btn btn-primary">Send Reset Link</button>

      </form>
    </div>
  </div>
</div>
</div>

<script src="forgot-password.js"></script>
</body>
</html>